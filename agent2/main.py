from typing import TypedDict, List
from langgraph.graph import StateGraph

class AgentState(TypedDict):
  values: List[int]
  name: str
  result: str

def process_values(state: AgentState) -> AgentState:
  """This function handles multiple different inputs"""
  state['result'] = f"Hi there {state['name']}! Your sum = {sum(state['values'])}"
  return state

graph = StateGraph(AgentState)
graph.add_node("processor", process_values)
graph.set_entry_point("processor")
graph.set_finish_point("processor")

app = graph.compile()
answer = app.invoke({ "name": "<PERSON>", "values": [1, 2, 3] })
print(answer['result'])


# 

class AgentState2(TypedDict):
  name: str
  values: List[int]
  operation: str
  result: str

def process_values2(state: AgentState2) -> AgentState2:
  """This function performs an operation as provided by the user"""
  if (state['operation'] == "*"):
    state['result'] = f"Hi {state['name']}!, your answer is {prod(state['values'])}"
  elif (state['operation'] == "+"):
    state['result'] = f"Hi {state['name']}!, your answer is {sum(state['values'])}"
  else:
    state['result'] = f"Hi {state['name']}!, I don't know how to do that"
  return state

graph2 = StateGraph(AgentState2)
graph2.add_node("processor2", process_values2)
graph2.set_entry_point("processor2")
graph2.set_finish_point("processor2")

app2 = graph2.compile()
answer2 = app2.invoke({ "name": "Bob", "values": [2, 3, 4], "operation": "+"})
print(answer2['result'])
